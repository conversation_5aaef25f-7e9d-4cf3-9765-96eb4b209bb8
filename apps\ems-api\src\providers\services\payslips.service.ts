import { Injectable } from '@nestjs/common';
import { HttpService } from 'packages/http';
import { PayslipsModel } from '../models/payslips/payslips.model';
import { PayslipsDocumentsAttachmentsPreviewAttachmentAttachmentsPreviewItemResponseModel } from '../models/payslips/payslipsDocumentsAttachmentsPreviewAttachmentAttachmentsPreviewItemResponse.model';
import { PayslipsDocumentsAttachmentsPreviewAttachmentItemResponseModel } from '../models/payslips/payslipsDocumentsAttachmentsPreviewAttachmentItemResponse.model';
import { PayslipsDocumentsAttachmentsPreviewItemResponseModel } from '../models/payslips/payslipsDocumentsAttachmentsPreviewItemResponse.model';
import { PayslipsDocumentsItemResponseModel } from '../models/payslips/payslipsDocumentsItemResponse.model';
import { PayslipsItemResponseModel } from '../models/payslips/payslipsItemResponse.model';
import { PayslipsPayslipHistoryItemResponseModel } from '../models/payslips/payslipsPayslipHistoryItemResponse.model';
import { PayslipsPayslipSummaryItemResponseModel } from '../models/payslips/payslipsPayslipSummaryItemResponse.model';
import { PayslipsPayslipSummaryNetPayDistributionItemResponseModel } from '../models/payslips/payslipsPayslipSummaryNetPayDistributionItemResponse.model';
@Injectable()
export class PayslipsService {
  constructor(private readonly http: HttpService) {}

  public async getDocumentAttachmentsPreview(
    payslipsUniqID: string,
    documentsUniqID: string,
    attachmentsPreviewUniqID: string,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
  ): Promise<PayslipsDocumentsAttachmentsPreviewItemResponseModel> {
    // -- not used
    const response =
      await this.http.get<PayslipsDocumentsAttachmentsPreviewItemResponseModel>(
        `payslips/${payslipsUniqID}/child/documents/${documentsUniqID}/child/AttachmentsPreview/${attachmentsPreviewUniqID}`,
        {
          payslipsUniqID: payslipsUniqID,
          documentsUniqID: documentsUniqID,
          AttachmentsPreviewUniqID: attachmentsPreviewUniqID,
          expand: expand,
          fields: fields,
          onlyData: onlyData,
          dependency: dependency,
          links: links,
        },
        false,
      );
    return response;
  }

  public async getDocumentFileContents(
    payslipsUniqID?: string,
    documentsUniqID?: string,
  ): Promise<PayslipsDocumentsItemResponseModel> {
    // -- not used
    const response = await this.http.get<PayslipsDocumentsItemResponseModel>(
      `payslips/${payslipsUniqID}/child/documents/${documentsUniqID}/enclosure/FileContents`,
      // { payslipsUniqID: payslipsUniqID, documentsUniqID: documentsUniqID },
      {},
      false,
      {
        responseType: 'arraybuffer',
      },
    );
    return response;
  }

  public async putDocumentFileContents(
    model: PayslipsDocumentsItemResponseModel,
  ): Promise<PayslipsDocumentsItemResponseModel> {
    // -- not used
    const response = await this.http.put<PayslipsDocumentsItemResponseModel>(
      `payslips/{payslipsUniqID}/child/documents/{documentsUniqID}/enclosure/FileContents`,
      model,
    );
    return response;
  }

  public async getAttachmentFileWebImage(
    payslipsUniqID: string,
    documentsUniqID: string,
    attachmentsPreviewUniqID: string,
    attachmentUniqID: string,
  ): Promise<PayslipsDocumentsAttachmentsPreviewAttachmentItemResponseModel> {
    // -- not used
    const response =
      await this.http.get<PayslipsDocumentsAttachmentsPreviewAttachmentItemResponseModel>(
        `payslips/${payslipsUniqID}/child/documents/${documentsUniqID}/child/AttachmentsPreview/${attachmentsPreviewUniqID}/child/Attachment/${attachmentUniqID}/enclosure/FileWebImage`,
        {
          payslipsUniqID: payslipsUniqID,
          documentsUniqID: documentsUniqID,
          AttachmentsPreviewUniqID: attachmentsPreviewUniqID,
          AttachmentUniqID: attachmentUniqID,
        },
        false,
      );
    return response;
  }

  public async getPayslips(
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    links?: string,
    limit?: number,
    offset?: number,
    totalResults?: boolean,
    q?: string,
    orderBy?: string,
    finder?: string,
  ): Promise<PayslipsModel[]> {
    // -- not used
    const response = await this.http.get<PayslipsModel[]>(
      `payslips`,
      {
        expand: expand,
        fields: fields,
        onlyData: onlyData,
        links: links,
        limit: limit,
        offset: offset,
        totalResults: totalResults,
        q: q,
        orderBy: orderBy,
        finder: finder,
      },
      true,
    );
    return response;
  }

  public async getNetPayDistribution(
    payslipsUniqID: string,
    payrollRelActionId: number,
    netPayDistributionUniqID: string,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
  ): Promise<PayslipsPayslipSummaryNetPayDistributionItemResponseModel> {
    // -- not used
    const response =
      await this.http.get<PayslipsPayslipSummaryNetPayDistributionItemResponseModel>(
        `payslips/${payslipsUniqID}/child/PayslipSummary/${payrollRelActionId}/child/NetPayDistribution/${netPayDistributionUniqID}`,
        {
          payslipsUniqID: payslipsUniqID,
          PayrollRelActionId: payrollRelActionId,
          NetPayDistributionUniqID: netPayDistributionUniqID,
          expand: expand,
          fields: fields,
          onlyData: onlyData,
          dependency: dependency,
          links: links,
        },
        false,
      );
    return response;
  }

  public async downloadPayslipAttachments(
    model: PayslipsItemResponseModel,
  ): Promise<PayslipsItemResponseModel> {
    // -- not used
    const response = await this.http.post<PayslipsItemResponseModel>(
      `payslips/action/downloadAttachments`,
      model,
    );
    return response;
  }

  public async getDocumentById(
    payslipsUniqID: string,
    documentsUniqID: string,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
  ): Promise<PayslipsDocumentsItemResponseModel> {
    // -- not used
    const response = await this.http.get<PayslipsDocumentsItemResponseModel>(
      `payslips/${payslipsUniqID}/child/documents/${documentsUniqID}`,
      {
        payslipsUniqID: payslipsUniqID,
        documentsUniqID: documentsUniqID,
        expand: expand,
        fields: fields,
        onlyData: onlyData,
        dependency: dependency,
        links: links,
      },
      false,
    );
    return response;
  }

  public async getPayslipById(
    payslipsUniqID?: string,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
  ): Promise<PayslipsItemResponseModel> {
    // -- not used
    const response = await this.http.get<PayslipsItemResponseModel>(
      `payslips/${payslipsUniqID}`,
      {
        // payslipsUniqID: payslipsUniqID,
        expand: expand,
        fields: fields,
        onlyData: onlyData,
        dependency: dependency,
        links: links,
      },
      false,
    );
    return response;
  }

  public async getAttachmentFileContents(
    payslipsUniqID: string,
    documentsUniqID: string,
    attachmentsPreviewUniqID: string,
    attachmentUniqID: string,
  ): Promise<PayslipsDocumentsAttachmentsPreviewAttachmentItemResponseModel> {
    // -- not used
    const response =
      await this.http.get<PayslipsDocumentsAttachmentsPreviewAttachmentItemResponseModel>(
        `payslips/${payslipsUniqID}/child/documents/${documentsUniqID}/child/AttachmentsPreview/${attachmentsPreviewUniqID}/child/Attachment/${attachmentUniqID}/enclosure/FileContents`,
        {
          payslipsUniqID: payslipsUniqID,
          documentsUniqID: documentsUniqID,
          AttachmentsPreviewUniqID: attachmentsPreviewUniqID,
          AttachmentUniqID: attachmentUniqID,
        },
        false,
      );
    return response;
  }

  public async putAttachmentFileContents(
    model: PayslipsDocumentsAttachmentsPreviewAttachmentItemResponseModel,
  ): Promise<PayslipsDocumentsAttachmentsPreviewAttachmentItemResponseModel> {
    // -- not used
    const response =
      await this.http.put<PayslipsDocumentsAttachmentsPreviewAttachmentItemResponseModel>(
        `payslips/{payslipsUniqID}/child/documents/{documentsUniqID}/child/AttachmentsPreview/{AttachmentsPreviewUniqID}/child/Attachment/{AttachmentUniqID}/enclosure/FileContents`,
        model,
      );
    return response;
  }

  public async getDocumentFileWebImage(
    payslipsUniqID: string,
    documentsUniqID: string,
  ): Promise<PayslipsDocumentsItemResponseModel> {
    // -- not used
    const response = await this.http.get<PayslipsDocumentsItemResponseModel>(
      `payslips/${payslipsUniqID}/child/documents/${documentsUniqID}/enclosure/FileWebImage`,
      { payslipsUniqID: payslipsUniqID, documentsUniqID: documentsUniqID },
      false,
    );
    return response;
  }

  public async getPayslipHistoryById(
    payslipsUniqID: string,
    payslipHistoryUniqID: string,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
  ): Promise<PayslipsPayslipHistoryItemResponseModel> {
    // -- not used
    const response =
      await this.http.get<PayslipsPayslipHistoryItemResponseModel>(
        `payslips/${payslipsUniqID}/child/payslipHistory/${payslipHistoryUniqID}`,
        {
          payslipsUniqID: payslipsUniqID,
          payslipHistoryUniqID: payslipHistoryUniqID,
          expand: expand,
          fields: fields,
          onlyData: onlyData,
          dependency: dependency,
          links: links,
        },
        false,
      );
    return response;
  }

  public async getAttachmentById(
    payslipsUniqID: string,
    documentsUniqID: string,
    attachmentsPreviewUniqID: string,
    attachmentUniqID: string,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
  ): Promise<PayslipsDocumentsAttachmentsPreviewAttachmentItemResponseModel> {
    // -- not used
    const response =
      await this.http.get<PayslipsDocumentsAttachmentsPreviewAttachmentItemResponseModel>(
        `payslips/${payslipsUniqID}/child/documents/${documentsUniqID}/child/AttachmentsPreview/${attachmentsPreviewUniqID}/child/Attachment/${attachmentUniqID}`,
        {
          payslipsUniqID: payslipsUniqID,
          documentsUniqID: documentsUniqID,
          AttachmentsPreviewUniqID: attachmentsPreviewUniqID,
          AttachmentUniqID: attachmentUniqID,
          expand: expand,
          fields: fields,
          onlyData: onlyData,
          dependency: dependency,
          links: links,
        },
        false,
      );
    return response;
  }

  public async getPayslipSummaryByPayrollId(
    payslipsUniqID: string,
    payrollRelActionId: number,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
  ): Promise<PayslipsPayslipSummaryItemResponseModel[]> {
    // -- not used
    const response = await this.http.get<
      PayslipsPayslipSummaryItemResponseModel[]
    >(
      `payslips/${payslipsUniqID}/child/PayslipSummary/${payrollRelActionId}`,
      {
        payslipsUniqID: payslipsUniqID,
        PayrollRelActionId: payrollRelActionId,
        expand: expand,
        fields: fields,
        onlyData: onlyData,
        dependency: dependency,
        links: links,
      },
      true,
    );
    return response;
  }

  public async getNestedAttachmentAttachmentsPreview(
    payslipsUniqID: string,
    documentsUniqID: string,
    attachmentsPreviewUniqID: string,
    attachmentUniqID: string,
    payslips_documents_AttachmentsPreview_Attachment_AttachmentsPreview_Id: string,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
  ): Promise<PayslipsDocumentsAttachmentsPreviewAttachmentAttachmentsPreviewItemResponseModel> {
    // -- not used
    const response =
      await this.http.get<PayslipsDocumentsAttachmentsPreviewAttachmentAttachmentsPreviewItemResponseModel>(
        `payslips/${payslipsUniqID}/child/documents/${documentsUniqID}/child/AttachmentsPreview/${attachmentsPreviewUniqID}/child/Attachment/${attachmentUniqID}/child/AttachmentsPreview/${payslips_documents_AttachmentsPreview_Attachment_AttachmentsPreview_Id}`,
        {
          payslipsUniqID: payslipsUniqID,
          documentsUniqID: documentsUniqID,
          AttachmentsPreviewUniqID: attachmentsPreviewUniqID,
          AttachmentUniqID: attachmentUniqID,
          payslips_documents_AttachmentsPreview_Attachment_AttachmentsPreview_Id:
            payslips_documents_AttachmentsPreview_Attachment_AttachmentsPreview_Id,
          expand: expand,
          fields: fields,
          onlyData: onlyData,
          dependency: dependency,
          links: links,
        },
        false,
      );
    return response;
  }
}

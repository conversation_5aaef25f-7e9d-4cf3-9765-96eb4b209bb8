import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Req,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { Response } from 'express';
import { ApiBody, ApiResponse } from '@nestjs/swagger';
import { ValueTransformPipe } from '../pipes/valuetransform.pipe';
import { DocumentDeliveryPreferencesV2Model } from '../providers/models/document/documentDeliveryPreferencesV2.model';
import { DocumentDeliveryPreferencesV2ItemPostRequestModel } from '../providers/models/document/documentDeliveryPreferencesV2ItemPostRequest.model';
import { DocumentDeliveryPreferencesV2ItemResponseModel } from '../providers/models/document/documentDeliveryPreferencesV2ItemResponse.model';
import { DocumentRecordsModel } from '../providers/models/document/documentRecords.model';
import { DocumentRecordsAttachmentsAttachmentsPreviewItemResponseModel } from '../providers/models/document/documentRecordsAttachmentsAttachmentsPreviewItemResponse.model';
import { DocumentRecordsAttachmentsItemPostRequestModel } from '../providers/models/document/documentRecordsAttachmentsItemPostRequest.model';
import { DocumentRecordsAttachmentsItemResponseModel } from '../providers/models/document/documentRecordsAttachmentsItemResponse.model';
import { DocumentRecordsAttachmentsPreviewItemResponseModel } from '../providers/models/document/documentRecordsAttachmentsPreviewItemResponse.model';
import { DocumentRecordsBannerOverrideMessagesItemResponseModel } from '../providers/models/document/documentRecordsBannerOverrideMessagesItemResponse.model';
import { DocumentRecordsDocumentRecordsDDFItemPostRequestModel } from '../providers/models/document/documentRecordsDocumentRecordsDDFItemPostRequest.model';
import { DocumentRecordsDocumentRecordsDDFItemResponseModel } from '../providers/models/document/documentRecordsDocumentRecordsDDFItemResponse.model';
import { DocumentRecordsDocumentRecordsDFFItemPostRequestModel } from '../providers/models/document/documentRecordsDocumentRecordsDFFItemPostRequest.model';
import { DocumentRecordsDocumentRecordsDFFItemResponseModel } from '../providers/models/document/documentRecordsDocumentRecordsDFFItemResponse.model';
import { DocumentRecordsHrDocumentTypesDocumentTypesDDFItemResponseModel } from '../providers/models/document/documentRecordsHrDocumentTypesDocumentTypesDDFItemResponse.model';
import { DocumentRecordsHrDocumentTypesDocumentTypesDFFItemResponseModel } from '../providers/models/document/documentRecordsHrDocumentTypesDocumentTypesDFFItemResponse.model';
import { DocumentRecordsHrDocumentTypesItemResponseModel } from '../providers/models/document/documentRecordsHrDocumentTypesItemResponse.model';
import { DocumentRecordsItemPostRequestModel } from '../providers/models/document/documentRecordsItemPostRequest.model';
import { DocumentRecordsItemResponseModel } from '../providers/models/document/documentRecordsItemResponse.model';
import { DocumentService } from '../providers/services/document.service';
// import {  DocumentRecordsHrDocumentTypeModel } from '../providers/models/document/documentRecordHrTypeLOV.model';
import { HrDocumentTypesLOVModel } from '../providers/models/hr/hrDocumentTypesLOV.model';
import { TasksService } from '../providers/services/tasks.service';
import { removeEmptyFields } from '../utils/cleanObject';

@Controller()
export class DocumentController {
  constructor(
    private readonly service: DocumentService,
    private readonly taskService: TasksService,
  ) { }

  @Get(
    'documentRecords/:documentsOfRecordId/child/documentRecordsDFF/:documentsOfRecordId3',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: DocumentRecordsDocumentRecordsDFFItemResponseModel })
  public async getDocumentRecordsDFF(
    @Query() query?: any,
  ): Promise<DocumentRecordsDocumentRecordsDFFItemResponseModel> {
    // -- not used
    const {
      documentsOfRecordId,
      documentsOfRecordId3,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getDocumentRecordsDFF(
      documentsOfRecordId,
      documentsOfRecordId3,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Post('documentRecords/action/checkViewUnpublishedAccess')
  @ApiBody({ type: DocumentRecordsItemResponseModel })
  @ApiResponse({ type: DocumentRecordsItemResponseModel })
  public async checkViewUnpublishedAccess(
    @Body() body: DocumentRecordsItemResponseModel,
  ): Promise<DocumentRecordsItemResponseModel> {
    // -- not used
    const response = await this.service.checkViewUnpublishedAccess(body);
    return response;
  }

  @Post(
    'documentRecords/:documentsOfRecordId/child/hrDocumentTypes/action/checkManageAccess',
  )
  @ApiBody({ type: DocumentRecordsHrDocumentTypesItemResponseModel })
  @ApiResponse({ type: DocumentRecordsHrDocumentTypesItemResponseModel })
  public async checkManageAccess(
    @Body() body: DocumentRecordsHrDocumentTypesItemResponseModel,
  ): Promise<DocumentRecordsHrDocumentTypesItemResponseModel> {
    // -- not used
    const response = await this.service.checkManageAccess(body);
    return response;
  }

  @Get(
    'documentRecords/:documentsOfRecordId/child/documentRecordsDDF/:documentsOfRecordId2',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: DocumentRecordsDocumentRecordsDDFItemResponseModel })
  public async getDocumentRecordsDDF(
    @Query() query?: any,
  ): Promise<DocumentRecordsDocumentRecordsDDFItemResponseModel> {
    // -- not used
    const {
      documentsOfRecordId,
      documentsOfRecordId2,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getDocumentRecordsDDF(
      documentsOfRecordId,
      documentsOfRecordId2,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Get(
    'documentRecords/:documentsOfRecordId/child/attachments/:attachmentsUniqID/child/attachmentsPreview/:attachmentsPreviewUniqID',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({
    type: DocumentRecordsAttachmentsAttachmentsPreviewItemResponseModel,
  })
  public async getDocumentRecordsAttachmentsPreview(
    @Query() query?: any,
  ): Promise<DocumentRecordsAttachmentsAttachmentsPreviewItemResponseModel> {
    // -- not used
    const {
      documentsOfRecordId,
      attachmentsUniqID,
      attachmentsPreviewUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getDocumentRecordsAttachmentsPreview(
      documentsOfRecordId,
      attachmentsUniqID,
      attachmentsPreviewUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Get(
    'documentRecords/:documentsOfRecordId/child/attachmentsPreview/:attachmentsPreviewUniqID/enclosure/fileContents',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: DocumentRecordsAttachmentsPreviewItemResponseModel })
  public async getAttachmentsPreviewFileContent(
    @Query() query?: any,
  ): Promise<DocumentRecordsAttachmentsPreviewItemResponseModel> {
    // -- not used
    const { documentsOfRecordId, attachmentsPreviewUniqID } = query;
    const response = await this.service.getAttachmentsPreviewFileContent(
      documentsOfRecordId,
      attachmentsPreviewUniqID,
    );
    return response;
  }

  @Post(
    'documentRecords/:documentsOfRecordId/child/attachmentsPreview/:attachmentsPreviewUniqID/enclosure/fileContents',
  )
  @ApiBody({ type: DocumentRecordsAttachmentsPreviewItemResponseModel })
  @ApiResponse({ type: DocumentRecordsAttachmentsPreviewItemResponseModel })
  public async putAttachmentsPreviewFileContent(
    @Body() body: DocumentRecordsAttachmentsPreviewItemResponseModel,
  ): Promise<DocumentRecordsAttachmentsPreviewItemResponseModel> {
    // -- not used
    const response = await this.service.putAttachmentsPreviewFileContent(body);
    return response;
  }

  @Post('documentRecords/action/checkPersonDocumentTypeCreateAccess')
  @ApiBody({ type: DocumentRecordsItemResponseModel })
  @ApiResponse({ type: DocumentRecordsItemResponseModel })
  public async checkPersonDocumentTypeCreateAccess(
    @Body() body: DocumentRecordsItemResponseModel,
  ): Promise<DocumentRecordsItemResponseModel> {
    // -- not used
    const response =
      await this.service.checkPersonDocumentTypeCreateAccess(body);
    return response;
  }

  @Post('documentRecords/:documentsOfRecordId/child/documentRecordsDFF')
  @ApiBody({ type: DocumentRecordsDocumentRecordsDFFItemPostRequestModel })
  @ApiResponse({ type: DocumentRecordsDocumentRecordsDFFItemPostRequestModel })
  public async postDocumentRecordsDFFList(
    @Body() body: DocumentRecordsDocumentRecordsDFFItemPostRequestModel,
  ): Promise<DocumentRecordsDocumentRecordsDFFItemPostRequestModel> {
    // -- not used
    const response = await this.service.postDocumentRecordsDFFList(body);
    return response;
  }

  @Post('documentRecords/action/isApprovalInProgress')
  @ApiBody({ type: DocumentRecordsItemResponseModel })
  @ApiResponse({ type: DocumentRecordsItemResponseModel })
  public async isApprovalInProgress(
    @Body() body: DocumentRecordsItemResponseModel,
  ): Promise<DocumentRecordsItemResponseModel> {
    // -- not used
    const response = await this.service.isApprovalInProgress(body);
    return response;
  }

  @Post('documentRecords/:documentsOfRecordId/child/documentRecordsDDF')
  @ApiBody({ type: DocumentRecordsDocumentRecordsDDFItemPostRequestModel })
  @ApiResponse({ type: DocumentRecordsDocumentRecordsDDFItemPostRequestModel })
  public async postDocumentRecordsDDFList(
    @Body() body: DocumentRecordsDocumentRecordsDDFItemPostRequestModel,
  ): Promise<DocumentRecordsDocumentRecordsDDFItemPostRequestModel> {
    // -- not used
    const response = await this.service.postDocumentRecordsDDFList(body);
    return response;
  }

  @Get('documentRecords')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<DocumentRecordsModel> })
  public async getDocumentRecords_2(
    @Query() query?: any,
    @Req() req?: any,
  ): Promise<DocumentRecordsModel> {
    if (!query) {
      query = {};
    }
    query.finder = `findByPersonIdAfterReload;PersonId=${req.user?.fusionProfile?.PersonId}`;
    const {
      expand,
      fields,
      onlyData,
      links,
      limit,
      offset,
      totalResults,
      orderBy,
      finder,
      q,
      search,
      startDate,
      endDate,
      status
    } = query;
    const dateFilter: boolean =
      startDate != null && startDate !== '' && endDate != null && endDate !== ''
        ? true
        : false;
    const response = await this.service.getDocumentRecords_2(
      expand,
      fields,
      onlyData,
      links,
      //  search ? 500 : limit,
      search || dateFilter ? 500 : limit,
      offset,
      totalResults,
      q,
      orderBy,
      finder,
    );

    const allowedDocumentTypeIds = [
      300000004595480,
      300000004595474,
      300000004595486,
      300000004595510,
      300000008089003,
      300000004595516,
      300000004595498,
      300000004595492
    ];

    response.items = response.items.filter((item) =>
      allowedDocumentTypeIds.includes(item.DocumentTypeId)
    );

    if (search) {
      response.items = response.items.filter((item) => {
        return (
          item.CreatedBy.toLowerCase().indexOf(search.toLowerCase()) !== -1 ||
          item.DocumentType.toLowerCase().indexOf(search.toLowerCase()) !== -1
        );
      });
    }
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      start.setHours(0, 0, 0, 0);
      end.setHours(23, 59, 59, 999);
      response.items = response.items.filter((document) => {
        if (!document.CreationDate) return false;
        const CreationDate = new Date(document.CreationDate);
        return CreationDate >= start && CreationDate <= end;
      });
    }
    response.items = response.items.sort((a, b) => {
      return new Date(b.CreationDate).getTime() - new Date(a.CreationDate).getTime();
    });

    const tasks = await this.taskService.getTasks();

    for (let i = 0; i < response.items.length; i++) {
      let record = response.items[i];
      const task = tasks.items.find(
        (task) => task.IdentificationKey === record.DocumentsOfRecordId,
      );

      record = {
        ...record,
        ...task,
      };
    }

    if (status) {
      if (status.toLowerCase() == 'pending') {
        response.items = response.items.filter((item) =>
          item?.bannerOverrideMessages?.length > 0
        );
        response.items.length || (response.hasMore = false);
      } else if (status.toLowerCase() == 'approved') {
        response.items = response.items.filter((item) =>
          item?.bannerOverrideMessages?.length == 0
        );
        (response.items.length < limit && response.hasMore == true) || (response.hasMore = false);
      }

    }
    return response;
  }




  @Get('documentTypeRecords')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<DocumentRecordsModel> })
  public async getDocumentTypeRecords_2(
    @Query() query?: any,
  ): Promise<DocumentRecordsModel> {
    const {
      expand,
      fields,
      onlyData,
      links,
      limit,
      offset,
      totalResults,
      orderBy,
      finder,
      q,
      search,
      startDate,
      endDate,
      status
    } = query;
    const dateFilter: boolean =
      startDate != null && startDate !== '' && endDate != null && endDate !== ''
        ? true
        : false;
    const response = await this.service.getDocumentRecords_2(
      expand,
      fields,
      onlyData,
      links,
      //  search ? 500 : limit,
      search || dateFilter ? 500 : limit,
      offset,
      totalResults,
      q,
      orderBy,
      finder,
    );

    const allowedDocumentTypeIds = [
      300000004595446,
      300000007197612,
      300000004595468,
      300000004875800,
      300000007197588,
      300000004595454,
      300000007197619,
      300000004875794,
      300000007197600,
      300000004595433,
      300000007197606,
      300000004595440,
      300000007197496,
      300000004595460,
      300000007197581,
      300000009069131
    ];

    response.items = response.items.filter((item) =>
      allowedDocumentTypeIds.includes(item.DocumentTypeId)
    );

    if (search) {
      response.items = response.items.filter((item) => {
        return (
          item.CreatedBy.toLowerCase().indexOf(search.toLowerCase()) !== -1 ||
          item.DocumentType.toLowerCase().indexOf(search.toLowerCase()) !== -1
        );
      });
    }
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      start.setHours(0, 0, 0, 0);
      end.setHours(23, 59, 59, 999);
      response.items = response.items.filter((document) => {
        if (!document.CreationDate) return false;
        const CreationDate = new Date(document.CreationDate);
        return CreationDate >= start && CreationDate <= end;
      });
    }
    response.items = response.items.sort((a, b) => {
      return new Date(b.CreationDate).getTime() - new Date(a.CreationDate).getTime();
    });

    const tasks = await this.taskService.getTasks();

    for (let i = 0; i < response.items.length; i++) {
      let record = response.items[i];
      const task = tasks.items.find(
        (task) => task.IdentificationKey === record.DocumentsOfRecordId,
      );

      record = {
        ...record,
        ...task,
      };
    }

    if (status) {
      if (status.toLowerCase() == 'pending') {
        response.items = response.items.filter((item) =>
          item?.bannerOverrideMessages?.length > 0
        );
        response.items.length || (response.hasMore = false);
      } else if (status.toLowerCase() == 'approved') {
        response.items = response.items.filter((item) =>
          item?.bannerOverrideMessages?.length == 0
        );
        (response.items.length < limit && response.hasMore == true) || (response.hasMore = false);
      }

    }
    return response;
  }

 


  @Get('documentRecords/status')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  public async getDocumentRecords_3(
    @Query() query?: any,
    @Req() req?: any,
  ) {
    if (!query) {
      query = {};
    }
    query.finder = `findByPersonIdAfterReload;PersonId=${req.user?.fusionProfile?.PersonId}`;
    const {
      expand = 'bannerOverrideMessages',
      fields,
      onlyData = true,
      links,
      limit = 1000,
      offset = 0,
      totalResults = true,
      q,
      orderBy,
      finder,
    } = query;
    const result = {
      pendingItems: 0,
      approvedItems: 0,
      rejectedItems: 0,
      withdrawnItems: 0,
    };

    let hasMore = true;
    let currentOffset = offset;

    while (hasMore) {
      const response = await this.service.getDocumentRecords_2(
        expand,
        fields,
        onlyData,
        links,
        limit,
        offset,
        totalResults,
        q,
        orderBy,
        finder,
      );
       const allowedDocumentTypeIds = [
      300000004595480,
      300000004595474,
      300000004595486,
      300000004595510,
      300000008089003,
      300000004595516,
      300000004595498,
      300000004595492
    ];

      const items = response.items.filter((item) =>
      allowedDocumentTypeIds.includes(item.DocumentTypeId)
    );
     

      items.forEach((item) => {
        if (item.bannerOverrideMessages?.length == 0) {
          result.approvedItems++;
        } else {
          result.pendingItems++;
        }
      });

      hasMore = response['hasMore'];
      currentOffset += response['count'] ?? items.length;
    }

    return result;
  }

  @Get('/hrDocumentTypesLOV')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: HrDocumentTypesLOVModel })
  public async getDocumentHrRecords(
    @Query() query?: any,
  ): Promise<HrDocumentTypesLOVModel> {
    // -- not used
    const {
      expand,
      fields,
      onlyData,
      links,
      limit,
      offset,
      totalResults,
      q,
      orderBy,
      finder,
    } = query;
    const response = await this.service.getDocumentHrRecords(
      expand,
      fields,
      onlyData,
      links,
      limit,
      offset,
      totalResults,
      q,
      orderBy,
      finder,
    );
    return response;
  }
  // ...................
  @Post('documentRecords')
  @ApiBody({ type: DocumentRecordsItemPostRequestModel })
  @ApiResponse({ type: DocumentRecordsItemPostRequestModel })
  public async postDocumentRecords_2(
    @Body() body: DocumentRecordsItemPostRequestModel,
    @Req() req?: any,
  ): Promise<DocumentRecordsItemPostRequestModel> {
    // -- not used
    body.PersonId = req.user?.fusionProfile?.PersonId;
    body.PersonNumber = req.user?.fusionProfile?.PersonNumber;
    const response = await this.service.postDocumentRecords_2(body);
    return response;
  }

  @Get(
    'documentRecords/:documentsOfRecordId/child/hrDocumentTypes/:documentTypeId/child/documentTypesDDF/:documentTypeId2',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({
    type: DocumentRecordsHrDocumentTypesDocumentTypesDDFItemResponseModel,
  })
  public async getDocumentTypesDDF(
    @Query() query?: any,
  ): Promise<DocumentRecordsHrDocumentTypesDocumentTypesDDFItemResponseModel> {
    // -- not used
    const {
      documentsOfRecordId,
      documentTypeId,
      documentTypeId2,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getDocumentTypesDDF(
      documentsOfRecordId,
      documentTypeId,
      documentTypeId2,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Get(
    'documentRecords/:documentsOfRecordId/child/attachmentsPreview/:attachmentsPreviewUniqID/enclosure/fileWebImage',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: DocumentRecordsAttachmentsPreviewItemResponseModel })
  public async getAttachmentsPreviewFileWebImage(
    @Query() query?: any,
  ): Promise<DocumentRecordsAttachmentsPreviewItemResponseModel> {
    // -- not used
    const { documentsOfRecordId, attachmentsPreviewUniqID } = query;
    const response = await this.service.getAttachmentsPreviewFileWebImage(
      documentsOfRecordId,
      attachmentsPreviewUniqID,
    );
    return response;
  }

  @Post('documentRecords/action/downloadAttachments')
  @ApiBody({ type: DocumentRecordsAttachmentsItemResponseModel })
  @ApiResponse({ type: DocumentRecordsAttachmentsItemResponseModel })
  public async downloadAttachments(
    @Body() body: DocumentRecordsAttachmentsItemResponseModel,
  ): Promise<DocumentRecordsAttachmentsItemResponseModel> {
    // -- not used
    const response = await this.service.downloadAttachments(body);
    return response;
  }

  @Post('documentRecords/action/checkManageAccess')
  @ApiBody({ type: DocumentRecordsItemResponseModel })
  @ApiResponse({ type: DocumentRecordsItemResponseModel })
  public async checkManageAccess_2(
    @Body() body: DocumentRecordsItemResponseModel,
  ): Promise<DocumentRecordsItemResponseModel> {
    // -- not used
    const response = await this.service.checkManageAccess_2(body);
    return response;
  }

  @Post('documentRecords/action/checkMassdownloadAccess')
  @ApiBody({ type: DocumentRecordsItemResponseModel })
  @ApiResponse({ type: DocumentRecordsItemResponseModel })
  public async checkMassdownloadAccess(
    @Body() body: DocumentRecordsItemResponseModel,
  ): Promise<DocumentRecordsItemResponseModel> {
    // -- not used
    const response = await this.service.checkMassdownloadAccess(body);
    return response;
  }

  @Post('documentRecords/action/checkPersonDocumentTypeEditAccess')
  @ApiBody({ type: DocumentRecordsItemResponseModel })
  @ApiResponse({ type: DocumentRecordsItemResponseModel })
  public async checkPersonDocumentTypeEditAccess(
    @Body() body: DocumentRecordsItemResponseModel,
  ): Promise<DocumentRecordsItemResponseModel> {
    // -- not used
    const response = await this.service.checkPersonDocumentTypeEditAccess(body);
    return response;
  }

  @Get(
    'documentRecords/:documentsOfRecordId/child/hrDocumentTypes/:documentTypeId',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: DocumentRecordsHrDocumentTypesItemResponseModel })
  public async getHRDocumentTypes(
    @Query() query?: any,
  ): Promise<DocumentRecordsHrDocumentTypesItemResponseModel> {
    // -- not used
    const {
      documentsOfRecordId,
      documentTypeId,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getHRDocumentTypes(
      documentsOfRecordId,
      documentTypeId,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  // @Get(
  //   'documentRecords/:documentsOfRecordId/child/attachmentsPreview',
  // )
  // @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  // @ApiResponse({ type: DocumentRecordsAttachmentsPreviewItemResponseModel })
  // public async getAttachmentsPreviewById(
  //   @Query() query?: any,
  // ): Promise<DocumentRecordsAttachmentsPreviewItemResponseModel> {
  //   // -- not used
  //   const {
  //     documentsOfRecordId,
  //     attachmentsPreviewUniqID,
  //     expand,
  //     fields,
  //     onlyData,
  //     dependency,
  //     links,
  //   } = query;
  //   const response = await this.service.getAttachmentsPreviewById(
  //     documentsOfRecordId,
  //     attachmentsPreviewUniqID,
  //     expand,
  //     fields,
  //     onlyData,
  //     dependency,
  //     links,
  //   );
  //   return response;
  // }

@Get('documentRecords/:doId/child/attachmentsPreview')
@UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
@ApiResponse({ type: DocumentRecordsAttachmentsPreviewItemResponseModel })
public async getAttachmentsPreviewById(
  @Param() params: any,
  @Query() query?: any,
): Promise<DocumentRecordsAttachmentsPreviewItemResponseModel> {
  const { doId: documentsOfRecordId } = params;
  const { expand, fields, onlyData, dependency, links } = query;
  
  const response = await this.service.getAttachmentsPreviewById(
    documentsOfRecordId ?? '',

    expand,
    fields,
    onlyData,
    dependency,
   
  );
  return response;
}
  @Post('documentRecords/:documentsOfRecordId/child/attachments')
  @ApiBody({ type: DocumentRecordsAttachmentsItemPostRequestModel })
  @ApiResponse({ type: DocumentRecordsAttachmentsItemPostRequestModel })
  public async postDocumentAttachments(
    @Body() body: DocumentRecordsAttachmentsItemPostRequestModel,
  ): Promise<DocumentRecordsAttachmentsItemPostRequestModel> {
    // -- not used
    const response = await this.service.postDocumentAttachments(body);
    return response;
  }

  @Get('documentRecords/:documentsOfRecordId/child/attachments')
  public async getDocumentAttachments(
    @Param('documentsOfRecordId') documentsOfRecordId: string,
  ): Promise<any> {
    // Now you can use documentsOfRecordId here
    return await this.service.getDocumentAttachments(documentsOfRecordId);
  }


  @Get(
    'documentRecords/:documentsOfRecordId/child/attachments/:attachmentsUniqID/enclosure/fileWebImage',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: DocumentRecordsAttachmentsItemResponseModel })
  public async getAttachmentFileWebImage(
    @Query() query?: any,
  ): Promise<DocumentRecordsAttachmentsItemResponseModel> {
    // -- not used
    const { documentsOfRecordId, attachmentsUniqID } = query;
    const response = await this.service.getAttachmentFileWebImage(
      documentsOfRecordId,
      attachmentsUniqID,
    );
    return response;
  }

  @Post('documentRecords/action/findByAdvancedSearchQuery')
  @ApiBody({ type: DocumentRecordsItemResponseModel })
  @ApiResponse({ type: DocumentRecordsItemResponseModel })
  public async findByAdvancedSearchQuery(
    @Body() body: DocumentRecordsItemResponseModel,
  ): Promise<DocumentRecordsItemResponseModel> {
    // -- not used
    const response = await this.service.findByAdvancedSearchQuery(body);
    return response;
  }

  @Get(
    'documentRecords/:documentsOfRecordId/child/attachments/:attachmentsUniqID',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: DocumentRecordsAttachmentsItemResponseModel })
  public async getAttachmentsById(
    @Query() query?: any,
  ): Promise<DocumentRecordsAttachmentsItemResponseModel> {
    // -- not used
    const {
      documentsOfRecordId,
      attachmentsUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getAttachmentsById(
      documentsOfRecordId,
      attachmentsUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }
  @Get('documentRecords/action/generateDraftLetter')
  @ApiBody({ type: DocumentRecordsItemResponseModel })
  @ApiResponse({ type: DocumentRecordsItemResponseModel })
  public async generateDraftLetter(
    @Query() query: any,
    @Res() res: any,
  ): Promise<any> {

    const dff = removeEmptyFields({
      __FLEX_Context: query.FlexContext,
      travellingCountry: query.TravellingCountry,
      travelStartDate: query.TravelStartDate,
      travelEndDate: query.TravelEndDate,
      addressTo: query.AddressTo,
    });
    const body = {
      attributeNameValues: {
        PersonId: query.PersonId,
        DocumentTypeId: query.DocumentTypeId,
        documentRecordsDFF: [dff],
      },
    };

    // Simulated base64 result (truncated for brevity)
    const result = (await this.service.generateDraftLetter(body)).result;

    const buffer = Buffer.from(result, 'base64');

    // const fileName = 'Draft_Letter.pdf';
    const fileName = query.FlexContext.replace("GLB_REQUEST_", "");

    // Set the headers to let the client know you're sending a file
    res.setHeader('Content-Disposition', `attachment; filename=${fileName}`);
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Length', buffer.length);

    // Write the buffer directly to the response stream
    res.end(buffer);
  }

  @Get(
    'documentRecords/:documentsOfRecordId/child/hrDocumentTypes/:documentTypeId/child/documentTypesDFF/:documentTypeId3',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({
    type: DocumentRecordsHrDocumentTypesDocumentTypesDFFItemResponseModel,
  })
  public async getDocumentTypesDFF(
    @Query() query?: any,
  ): Promise<DocumentRecordsHrDocumentTypesDocumentTypesDFFItemResponseModel> {
    // -- not used
    const {
      documentsOfRecordId,
      documentTypeId,
      documentTypeId3,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getDocumentTypesDFF(
      documentsOfRecordId,
      documentTypeId,
      documentTypeId3,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Post(
    'documentRecords/:documentsOfRecordId/child/hrDocumentTypes/action/checkManageDocumentTypeAccess',
  )
  @ApiBody({ type: DocumentRecordsHrDocumentTypesItemResponseModel })
  @ApiResponse({ type: DocumentRecordsHrDocumentTypesItemResponseModel })
  public async checkManageDocumentTypeAccess(
    @Body() body: DocumentRecordsHrDocumentTypesItemResponseModel,
  ): Promise<DocumentRecordsHrDocumentTypesItemResponseModel> {
    // -- not used
    const response = await this.service.checkManageDocumentTypeAccess(body);
    return response;
  }

  @Get('documentRecords/:documentsOfRecordId')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: DocumentRecordsItemResponseModel })
  public async getDocumentRecords(
    @Query() query?: any,
  ): Promise<DocumentRecordsItemResponseModel> {
    // -- not used
    const { documentsOfRecordId, expand, fields, onlyData, dependency, links } =
      query;
    const response = await this.service.getDocumentRecords(
      documentsOfRecordId,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Post('documentRecords/action/checkPersonDocumentTypeManageAccess')
  @ApiBody({ type: DocumentRecordsItemResponseModel })
  @ApiResponse({ type: DocumentRecordsItemResponseModel })
  public async checkPersonDocumentTypeManageAccess(
    @Body() body: DocumentRecordsItemResponseModel,
  ): Promise<DocumentRecordsItemResponseModel> {
    // -- not used
    const response =
      await this.service.checkPersonDocumentTypeManageAccess(body);
    return response;
  }

  @Get(
    'documentRecords/:documentsOfRecordId/child/bannerOverrideMessages/:objectId',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: DocumentRecordsBannerOverrideMessagesItemResponseModel })
  public async getBannerOverrideMessagesById(
    @Query() query?: any,
  ): Promise<DocumentRecordsBannerOverrideMessagesItemResponseModel> {
    // -- not used
    const {
      documentsOfRecordId,
      objectId,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getBannerOverrideMessagesById(
      documentsOfRecordId,
      objectId,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Post('documentRecords/action/checkPersonDocumentTypeDeleteAccess')
  @ApiBody({ type: DocumentRecordsItemResponseModel })
  @ApiResponse({ type: DocumentRecordsItemResponseModel })
  public async checkPersonDocumentTypeDeleteAccess(
    @Body() body: DocumentRecordsItemResponseModel,
  ): Promise<DocumentRecordsItemResponseModel> {
    // -- not used
    const response =
      await this.service.checkPersonDocumentTypeDeleteAccess(body);
    return response;
  }

  @Get(
    'documentRecords/:documentsOfRecordId/child/attachments/:attachmentsUniqID/enclosure/fileContents',
  )
  @UsePipes(new ValidationPipe({ transform: true }))
  @ApiResponse({ type: DocumentRecordsAttachmentsItemResponseModel })
  public async getFileContents(
    @Param('documentsOfRecordId') documentsOfRecordId: number,
    @Param('attachmentsUniqID') attachmentsUniqID: string,
    @Res() res: Response,

  ): Promise<void> {
    // -- not used
    // const { documentsOfRecordId, attachmentsUniqID } = query;
    const fileBuffer = await this.service.getFileContents(documentsOfRecordId, attachmentsUniqID);

    if (!fileBuffer) {
      res.status(404).send('File not found');
      return;
    }

    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': 'inline; filename="document.pdf"',
      'Content-Length': fileBuffer.length
    });

    res.send(fileBuffer);
  }

  @Post(
    'documentRecords/:documentsOfRecordId/child/attachments/:attachmentsUniqID/enclosure/fileContents',
  )
  @ApiBody({ type: DocumentRecordsAttachmentsItemResponseModel })
  @ApiResponse({ type: DocumentRecordsAttachmentsItemResponseModel })
  public async putFileContents(
    @Body() body: DocumentRecordsAttachmentsItemResponseModel,
  ): Promise<DocumentRecordsAttachmentsItemResponseModel> {
    // -- not used
    const response = await this.service.putFileContents(body);
    return response;
  }

  @Get('documentDeliveryPreferencesV2/:documentDeliveryPreferencesV2UniqID')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<DocumentDeliveryPreferencesV2ItemResponseModel> })
  public async getPreferences(
    @Query() query?: any,
  ): Promise<DocumentDeliveryPreferencesV2ItemResponseModel[]> {
    // -- not used
    const {
      documentDeliveryPreferencesV2UniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
      effectiveOf,
    } = query;
    const response = await this.service.getPreferences(
      documentDeliveryPreferencesV2UniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
      effectiveOf,
    );
    return response;
  }

  @Get('documentDeliveryPreferencesV2')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<DocumentDeliveryPreferencesV2Model> })
  public async getDocumentPreferences(
    @Query() query?: any,
  ): Promise<DocumentDeliveryPreferencesV2Model[]> {
    // -- not used
    const {
      expand,
      fields,
      onlyData,
      links,
      limit,
      offset,
      totalResults,
      q,
      orderBy,
      finder,
      effectiveDate,
      effectiveOf,
    } = query;
    const response = await this.service.getDocumentPreferences(
      expand,
      fields,
      onlyData,
      links,
      limit,
      offset,
      totalResults,
      q,
      orderBy,
      finder,
      effectiveDate,
      effectiveOf,
    );
    return response;
  }

  @Post('documentDeliveryPreferencesV2')
  @ApiBody({ type: DocumentDeliveryPreferencesV2ItemPostRequestModel })
  @ApiResponse({ type: DocumentDeliveryPreferencesV2ItemPostRequestModel })
  public async postDocumentPreferences(
    @Body() body: DocumentDeliveryPreferencesV2ItemPostRequestModel,
  ): Promise<DocumentDeliveryPreferencesV2ItemPostRequestModel> {
    // -- not used
    const response = await this.service.postDocumentPreferences(body);
    return response;
  }
}

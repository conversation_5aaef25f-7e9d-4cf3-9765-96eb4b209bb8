/* eslint-disable prettier/prettier */
import {
  Body,
  Controller,
  Get,
  Param,
  Put,
  Query,
  Req,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiResponse } from '@nestjs/swagger';
import { ValueTransformPipe } from '../pipes/valuetransform.pipe';
import { TasksModel } from '../providers/models/tasks/tasks.model';
import { TasksChangedAttributesItemResponseModel } from '../providers/models/tasks/tasksChangedAttributesItemResponse.model';
import { TasksItemResponseModel } from '../providers/models/tasks/tasksItemResponse.model';
import { TasksService } from '../providers/services/tasks.service';
@Controller()
export class TasksController {
  constructor(private readonly service: TasksService) {}

  @Get(
    'tasks/:identificationKey/child/changedAttributes/:changedAttributesUniqID',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: TasksChangedAttributesItemResponseModel })
  public async getChangedAttributesById(
    @Query() query?: any,
  ): Promise<TasksChangedAttributesItemResponseModel> {
    // -- not used
    const {
      identificationKey,
      changedAttributesUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getChangedAttributesById(
      identificationKey,
      changedAttributesUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Get('tasks/:identificationKey')
  // @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<TasksItemResponseModel> })
  public async getTasksById(
    @Query() query?: any,
    @Param('identificationKey') identificationKey?: string,
  ): Promise<TasksItemResponseModel[]> {
    // -- not used
    const { expand, fields, onlyData, dependency, links } = query;
    const response = await this.service.getTasksById(
      identificationKey,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }
  @Get('tasksCount')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<TasksModel> })
  public async getTasksCount(@Query() query?: any): Promise<TasksModel[]> {
    // -- not used
    const {
      expand,
      fields,
      identificationKey,
      onlyData,
      links,
      offset,
      totalResults,
      q,
      orderBy,
      finder,
    } = query;
    const allowedStatus = [
      'ASSIGNED',
      'COMPLETED',
      'SUSPENDED',
      'WITHDRAWN',
      'EXPIRED',
      'ERRORED',
      'ALERTED',
    ];

    const responseObj: any = {};
    for (let i = 0; i < allowedStatus.length; i++) {
      const status = allowedStatus[i];
      responseObj[status] = await this.service.getTasks(
        expand,
        identificationKey,
        'ALL',
        fields,
        onlyData,
        links,
        1,
        offset,
        totalResults,
        q,
        status,
        orderBy,
        finder,
      );
    }
    return responseObj;
  }

  @Get('tasks')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<TasksModel> })
  public async getTasks(@Query() query?: any): Promise<TasksModel> {
    // -- not used

    const {
      expand,
      identificationKey,
      assignment,
      fields,
      forApproval,
      forAbsences,
      onlyData,
      links,
      limit,
      offset,
      totalResults,
      q,
      status,
      orderBy,
      finder,
      startDate,
      endDate,
      search,
    } = query;
    const dateFilter: boolean =
      startDate != null && startDate !== '' && endDate != null && endDate !== ''
        ? true
        : false;

    const response = await this.service.getTasks(
      expand,
      identificationKey,
      assignment,
      fields,
      onlyData,
      links,
      //  dateFilter ? 500 : limit,
      search || dateFilter ? 500 : limit,

      offset,
      totalResults,
      q,
      status,
      orderBy || 'createdDate:desc',
      finder,
    );
    // temporarily used category instead of taskDefinitionName And titlePrefix as FYI. IT SHOULD BE CHANGED
    if (forApproval) {
      response.items = response.items.filter(
        (task) =>
          (task.category === 'Manager (e.g. Promotion, Transfer)' ||
            task.category === 'GlobalAbsenceApproval'||
            task.category === 'Talent (e.g. Goals, Performance)')
            &&
          (!task.titlePrefix || task.titlePrefix == 'Action Required' || task.titlePrefix == 'FYI'),
      );
    }

    if (forAbsences) {
      response.items = response.items.filter(
        (task) => task.taskDefinitionName === 'AbsencesApprovalsTask',
      );
    }

    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      start.setHours(0, 0, 0, 0);
      end.setHours(23, 59, 59, 999);
      response.items = response.items.filter((task) => {
        if (!task.createdDate) return false;
        const createdDate = new Date(task.createdDate);
        return createdDate >= start && createdDate <= end;
      });
    }
    if (search) {
      response.items = response.items.filter((item) => {
        return (
          (item.createdBy?.toLowerCase() ?? '').includes(
            search.toLowerCase(),
          ) || (item.title?.toLowerCase() ?? '').includes(search.toLowerCase())
        );
      });
    }
    // if (search) {
    //     response.items = response.items.filter((item) => {
    //       return (
    //        (item.createdBy.toLowerCase().indexOf(search.toLowerCase()) !== -1 || item.title.toLowerCase().indexOf(search.toLowerCase()) !== -1)
    //       );
    //     });
    //   }
    return response;
  }

  @Get('tasks/:id/history')
  // @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: [TasksModel] })
  public async getTask(
    @Param('id') id: string,
    @Query() query?: any,
  ): Promise<TasksModel[]> {
    // -- not used
    console.log('No query parameters provided', query);
    const response = await this.service.getTask(id);
    return response;
  }

  @Put('tasks')
  // @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Object })
  public async putTasks(
    @Body('tasks') taskIds: string[],
    @Body('action') action: { id: string },
    @Body('comment') comment: { commentStr: string },
    @Req() req?: any,
  ): Promise<any> {
    // Extract username from fusion profile
    const username = req.user?.fusionProfile?.PersonId;

    return this.service.putTasks(
      taskIds,
      action.id,
      comment.commentStr,
      username,
    );
  }
}

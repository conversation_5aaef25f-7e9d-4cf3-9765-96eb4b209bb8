import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiBody, ApiResponse } from '@nestjs/swagger';
import { ValueTransformPipe } from '../pipes/valuetransform.pipe';
import { PayslipsModel } from '../providers/models/payslips/payslips.model';
import { PayslipsDocumentsAttachmentsPreviewAttachmentAttachmentsPreviewItemResponseModel } from '../providers/models/payslips/payslipsDocumentsAttachmentsPreviewAttachmentAttachmentsPreviewItemResponse.model';
import { PayslipsDocumentsAttachmentsPreviewAttachmentItemResponseModel } from '../providers/models/payslips/payslipsDocumentsAttachmentsPreviewAttachmentItemResponse.model';
import { PayslipsDocumentsAttachmentsPreviewItemResponseModel } from '../providers/models/payslips/payslipsDocumentsAttachmentsPreviewItemResponse.model';
import { PayslipsDocumentsItemResponseModel } from '../providers/models/payslips/payslipsDocumentsItemResponse.model';
import { PayslipsItemResponseModel } from '../providers/models/payslips/payslipsItemResponse.model';
import { PayslipsPayslipHistoryItemResponseModel } from '../providers/models/payslips/payslipsPayslipHistoryItemResponse.model';
import { PayslipsPayslipSummaryItemResponseModel } from '../providers/models/payslips/payslipsPayslipSummaryItemResponse.model';
import { PayslipsPayslipSummaryNetPayDistributionItemResponseModel } from '../providers/models/payslips/payslipsPayslipSummaryNetPayDistributionItemResponse.model';
import { PayslipsService } from '../providers/services/payslips.service';
@Controller()
export class PayslipsController {
  constructor(private readonly service: PayslipsService) {}

  @Get(
    'payslips/:payslipsUniqID/child/documents/:documentsUniqID/child/attachmentsPreview/:attachmentsPreviewUniqID',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: PayslipsDocumentsAttachmentsPreviewItemResponseModel })
  public async getDocumentAttachmentsPreview(
    @Query() query?: any,
  ): Promise<PayslipsDocumentsAttachmentsPreviewItemResponseModel> {
    // -- not used
    const {
      payslipsUniqID,
      documentsUniqID,
      attachmentsPreviewUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getDocumentAttachmentsPreview(
      payslipsUniqID,
      documentsUniqID,
      attachmentsPreviewUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Get(
    'payslips/:payslipsUniqID/child/documents/:documentsUniqID/enclosure/fileContents',
  )
  // @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: PayslipsDocumentsItemResponseModel })
  public async getDocumentFileContents(
    @Query() query?: any,
    @Param('payslipsUniqID') payslipsUniqID?: string,
    @Param('documentsUniqID') documentsUniqID?: string,
    @Res() res?: any,
  ): Promise<any> {
    // -- not used
    // let { payslipsUniqID, documentsUniqID } = query;
    const response = await this.service.getDocumentFileContents(
      payslipsUniqID,
      documentsUniqID,
    );
    const rawPdfContent: any = response;
    console.log(rawPdfContent.slice(0, 10));
    const buffer = Buffer.from(rawPdfContent);

    // Set headers to trigger download
    res.setHeader(
      'Content-Disposition',
      'attachment; filename="' + documentsUniqID + '.pdf"',
    );
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Length', buffer.length);

    // Send the buffer directly in the response
    res.end(buffer);
  }

  @Post(
    'payslips/:payslipsUniqID/child/documents/:documentsUniqID/enclosure/fileContents',
  )
  @ApiBody({ type: PayslipsDocumentsItemResponseModel })
  @ApiResponse({ type: PayslipsDocumentsItemResponseModel })
  public async putDocumentFileContents(
    @Body() body: PayslipsDocumentsItemResponseModel,
  ): Promise<PayslipsDocumentsItemResponseModel> {
    // -- not used
    const response = await this.service.putDocumentFileContents(body);
    return response;
  }

  @Get(
    'payslips/:payslipsUniqID/child/documents/:documentsUniqID/child/attachmentsPreview/:attachmentsPreviewUniqID/child/attachment/:attachmentUniqID/enclosure/fileWebImage',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({
    type: PayslipsDocumentsAttachmentsPreviewAttachmentItemResponseModel,
  })
  public async getAttachmentFileWebImage(
    @Query() query?: any,
  ): Promise<PayslipsDocumentsAttachmentsPreviewAttachmentItemResponseModel> {
    // -- not used
    const {
      payslipsUniqID,
      documentsUniqID,
      attachmentsPreviewUniqID,
      attachmentUniqID,
    } = query;
    const response = await this.service.getAttachmentFileWebImage(
      payslipsUniqID,
      documentsUniqID,
      attachmentsPreviewUniqID,
      attachmentUniqID,
    );
    return response;
  }

  @Get('payslips')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<PayslipsModel> })
  public async getPayslips(@Query() query?: any): Promise<PayslipsModel[]> {
    // -- not used
    const {
      expand,
      fields,
      onlyData,
      links,
      limit,
      offset,
      totalResults,
      q,
      orderBy,
      finder,
    } = query;
    const response = await this.service.getPayslips(
      expand,
      fields,
      onlyData,
      links,
      limit,
      offset,
      totalResults,
      q,
      orderBy,
      finder,
    );
    return response;
  }

  @Get(
    'payslips/:payslipsUniqID/child/payslipSummary/:payrollRelActionId/child/netPayDistribution/:netPayDistributionUniqID',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({
    type: PayslipsPayslipSummaryNetPayDistributionItemResponseModel,
  })
  public async getNetPayDistribution(
    @Query() query?: any,
  ): Promise<PayslipsPayslipSummaryNetPayDistributionItemResponseModel> {
    // -- not used
    const {
      payslipsUniqID,
      payrollRelActionId,
      netPayDistributionUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getNetPayDistribution(
      payslipsUniqID,
      payrollRelActionId,
      netPayDistributionUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Post('payslips/action/downloadAttachments')
  @ApiBody({ type: PayslipsItemResponseModel })
  @ApiResponse({ type: PayslipsItemResponseModel })
  public async downloadPayslipAttachments(
    @Body() body: PayslipsItemResponseModel,
  ): Promise<PayslipsItemResponseModel> {
    // -- not used
    const response = await this.service.downloadPayslipAttachments(body);
    return response;
  }

  @Get('payslips/:payslipsUniqID/child/documents/:documentsUniqID')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: PayslipsDocumentsItemResponseModel })
  public async getDocumentById(
    @Query() query?: any,
  ): Promise<PayslipsDocumentsItemResponseModel> {
    // -- not used
    const {
      payslipsUniqID,
      documentsUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getDocumentById(
      payslipsUniqID,
      documentsUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Get('payslips/:payslipsUniqID')
  // @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: PayslipsItemResponseModel })
  public async getPayslipById(
    @Query() query?: any,
    @Param('payslipsUniqID') payslipsUniqID?: string,
  ): Promise<PayslipsItemResponseModel> {
    // -- not used
    const { expand, fields, onlyData, dependency, links } = query;
    const response = await this.service.getPayslipById(
      payslipsUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Get(
    'payslips/:payslipsUniqID/child/documents/:documentsUniqID/child/attachmentsPreview/:attachmentsPreviewUniqID/child/attachment/:attachmentUniqID/enclosure/fileContents',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({
    type: PayslipsDocumentsAttachmentsPreviewAttachmentItemResponseModel,
  })
  public async getAttachmentFileContents(
    @Query() query?: any,
  ): Promise<PayslipsDocumentsAttachmentsPreviewAttachmentItemResponseModel> {
    // -- not used
    const {
      payslipsUniqID,
      documentsUniqID,
      attachmentsPreviewUniqID,
      attachmentUniqID,
    } = query;
    const response = await this.service.getAttachmentFileContents(
      payslipsUniqID,
      documentsUniqID,
      attachmentsPreviewUniqID,
      attachmentUniqID,
    );
    return response;
  }

  @Post(
    'payslips/:payslipsUniqID/child/documents/:documentsUniqID/child/attachmentsPreview/:attachmentsPreviewUniqID/child/attachment/:attachmentUniqID/enclosure/fileContents',
  )
  @ApiBody({
    type: PayslipsDocumentsAttachmentsPreviewAttachmentItemResponseModel,
  })
  @ApiResponse({
    type: PayslipsDocumentsAttachmentsPreviewAttachmentItemResponseModel,
  })
  public async putAttachmentFileContents(
    @Body()
    body: PayslipsDocumentsAttachmentsPreviewAttachmentItemResponseModel,
  ): Promise<PayslipsDocumentsAttachmentsPreviewAttachmentItemResponseModel> {
    // -- not used
    const response = await this.service.putAttachmentFileContents(body);
    return response;
  }

  @Get(
    'payslips/:payslipsUniqID/child/documents/:documentsUniqID/enclosure/fileWebImage',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: PayslipsDocumentsItemResponseModel })
  public async getDocumentFileWebImage(
    @Query() query?: any,
  ): Promise<PayslipsDocumentsItemResponseModel> {
    // -- not used
    const { payslipsUniqID, documentsUniqID } = query;
    const response = await this.service.getDocumentFileWebImage(
      payslipsUniqID,
      documentsUniqID,
    );
    return response;
  }

  @Get('payslips/:payslipsUniqID/child/payslipHistory/:payslipHistoryUniqID')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: PayslipsPayslipHistoryItemResponseModel })
  public async getPayslipHistoryById(
    @Query() query?: any,
  ): Promise<PayslipsPayslipHistoryItemResponseModel> {
    // -- not used
    const {
      payslipsUniqID,
      payslipHistoryUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getPayslipHistoryById(
      payslipsUniqID,
      payslipHistoryUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Get(
    'payslips/:payslipsUniqID/child/documents/:documentsUniqID/child/attachmentsPreview/:attachmentsPreviewUniqID/child/attachment/:attachmentUniqID',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({
    type: PayslipsDocumentsAttachmentsPreviewAttachmentItemResponseModel,
  })
  public async getAttachmentById(
    @Query() query?: any,
  ): Promise<PayslipsDocumentsAttachmentsPreviewAttachmentItemResponseModel> {
    // -- not used
    const {
      payslipsUniqID,
      documentsUniqID,
      attachmentsPreviewUniqID,
      attachmentUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getAttachmentById(
      payslipsUniqID,
      documentsUniqID,
      attachmentsPreviewUniqID,
      attachmentUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Get('payslips/:payslipsUniqID/child/payslipSummary/:payrollRelActionId')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<PayslipsPayslipSummaryItemResponseModel> })
  public async getPayslipSummaryByPayrollId(
    @Query() query?: any,
  ): Promise<PayslipsPayslipSummaryItemResponseModel[]> {
    // -- not used
    const {
      payslipsUniqID,
      payrollRelActionId,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getPayslipSummaryByPayrollId(
      payslipsUniqID,
      payrollRelActionId,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Get(
    'payslips/:payslipsUniqID/child/documents/:documentsUniqID/child/attachmentsPreview/:attachmentsPreviewUniqID/child/attachment/:attachmentUniqID/child/attachmentsPreview/:payslips_documents_AttachmentsPreview_Attachment_AttachmentsPreview_Id',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({
    type: PayslipsDocumentsAttachmentsPreviewAttachmentAttachmentsPreviewItemResponseModel,
  })
  public async getNestedAttachmentAttachmentsPreview(
    @Query() query?: any,
  ): Promise<PayslipsDocumentsAttachmentsPreviewAttachmentAttachmentsPreviewItemResponseModel> {
    // -- not used
    const {
      payslipsUniqID,
      documentsUniqID,
      attachmentsPreviewUniqID,
      attachmentUniqID,
      payslips_documents_AttachmentsPreview_Attachment_AttachmentsPreview_Id,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getNestedAttachmentAttachmentsPreview(
      payslipsUniqID,
      documentsUniqID,
      attachmentsPreviewUniqID,
      attachmentUniqID,
      payslips_documents_AttachmentsPreview_Attachment_AttachmentsPreview_Id,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }
}
